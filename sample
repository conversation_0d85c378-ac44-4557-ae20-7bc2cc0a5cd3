{"payload": {"identifier": "***************", "first_seen": "2025-06-02T04:48:42.528318717Z", "last_seen": "2025-06-02T04:48:42.528318717Z", "detection": {"detection_ts": "2025-06-02T04:48:42.528318717Z", "category": ["<PERSON><PERSON>"], "risk": 100, "intensity": 1}, "meta": {"port": 8025, "country_code": "DE", "protocol": "tcp", "object_type": "ipv4"}, "detection_methods": ["Mailhog"], "action": "=", "type": "ip"}, "timestamp": "2025-06-02T04:48:42.528318717Z", "offset": 37251728}