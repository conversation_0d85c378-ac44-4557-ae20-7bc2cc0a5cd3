# Docker Deployment Guide

This guide covers how to run the Zombie IP S3 Ingest application using Docker.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Access to AWS S3 and MSSQL Server

## Quick Start

1. **Clone the repository and navigate to the project directory**

2. **Create environment file**:
   ```bash
   cp .env.docker .env
   # Edit .env with your actual configuration values
   ```

3. **Build and run with Docker Compose**:
   ```bash
   docker-compose up --build
   ```

## Configuration

### Environment Variables

The application requires several environment variables. Create a `.env` file based on `.env.docker`:

```bash
# Copy the template
cp .env.docker .env

# Edit with your values
nano .env  # or your preferred editor
```

### Required Variables

- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key  
- `S3_BUCKET_NAME`: Name of the S3 bucket containing IP data

### Optional Variables

- `AWS_REGION`: AWS region (default: us-east-1)
- `S3_PREFIX`: Prefix/folder path within the bucket
- `FILE_BATCH_SIZE`: Files per batch (default: 10)
- `MAX_WORKERS`: Parallel workers (default: 4)
- `MSSQL_SERVER`: SQL Server hostname (for database integration)
- `MSSQL_USERNAME`: Database username
- `MSSQL_PASSWORD`: Database password

## Docker Commands

### Build the Image
```bash
docker build -t zombie-ip-s3-ingest .
```

### Run with Docker
```bash
docker run --env-file .env zombie-ip-s3-ingest
```

### Run with Docker Compose
```bash
# Build and run
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Development Mode
```bash
# Run with volume mount for development
docker run --env-file .env \
  -v $(pwd):/app \
  zombie-ip-s3-ingest
```

## Image Details

### Base Image
- **Base**: Python 3.11 slim
- **Size**: ~200MB (optimized multi-stage build)
- **Architecture**: linux/amd64

### Included Components
- Python 3.11 runtime
- Microsoft ODBC Driver 17 for SQL Server
- All Python dependencies from requirements.txt
- Non-root user for security

### Security Features
- Runs as non-root user (`appuser`)
- Minimal attack surface (slim base image)
- No unnecessary packages in final image
- Environment variable validation

## Troubleshooting

### ODBC Connection Issues
If you encounter ODBC connection problems:

1. **Check driver installation**:
   ```bash
   docker run zombie-ip-s3-ingest odbcinst -q -d
   ```

2. **Test connection manually**:
   ```bash
   docker run --env-file .env zombie-ip-s3-ingest python -c "
   import pyodbc
   print('Available drivers:', [x for x in pyodbc.drivers()])
   "
   ```

### AWS Connection Issues
1. Verify AWS credentials are correctly set in `.env`
2. Check S3 bucket permissions
3. Ensure bucket name and region are correct

### Memory Issues
If the container runs out of memory:

1. **Increase Docker memory limit**
2. **Reduce batch size**:
   ```bash
   FILE_BATCH_SIZE=5
   MAX_WORKERS=2
   ```

### Logs and Debugging
```bash
# View container logs
docker-compose logs zombie-ip-ingest

# Run with debug shell
docker run --env-file .env -it zombie-ip-s3-ingest bash

# Check container status
docker-compose ps
```

## Production Deployment

### Resource Limits
The docker-compose.yml includes resource limits:
- Memory: 1GB limit, 512MB reservation
- CPU: 1.0 limit, 0.5 reservation

Adjust based on your workload:
```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '2.0'
```

### Health Checks
Health checks are configured to monitor container status:
```yaml
healthcheck:
  test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### Logging
Logs are configured with rotation:
```yaml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Persistent Storage
Mount volumes for logs or data persistence:
```yaml
volumes:
  - ./logs:/app/logs:rw
  - ./data:/app/data:rw
```

## Advanced Configuration

### Custom Dockerfile
To customize the Docker image, modify the Dockerfile:

```dockerfile
# Add custom packages
RUN apt-get update && apt-get install -y \
    your-custom-package \
    && rm -rf /var/lib/apt/lists/*

# Add custom Python packages
RUN pip install your-custom-package
```

### Multi-Architecture Builds
Build for multiple architectures:
```bash
docker buildx build --platform linux/amd64,linux/arm64 -t zombie-ip-s3-ingest .
```

### Container Registry
Push to a container registry:
```bash
# Tag for registry
docker tag zombie-ip-s3-ingest your-registry.com/zombie-ip-s3-ingest:latest

# Push
docker push your-registry.com/zombie-ip-s3-ingest:latest
```
