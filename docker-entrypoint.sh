#!/bin/bash
set -e

# Docker entrypoint script for Zombie IP S3 Ingest application

echo "Starting Zombie IP S3 Ingest application..."

# Verify ODBC driver installation
echo "Checking ODBC drivers..."
odbcinst -q -d

# Verify Python environment
echo "Python version: $(python --version)"
echo "Python path: $(which python)"

# Check if required environment variables are set
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$S3_BUCKET_NAME" ]; then
    echo "WARNING: Required AWS environment variables may not be set:"
    echo "  - AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:+SET}"
    echo "  - AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:+SET}"
    echo "  - S3_BUCKET_NAME: ${S3_BUCKET_NAME:+SET}"
fi

# Check MSSQL configuration if provided
if [ -n "$MSSQL_SERVER" ]; then
    echo "MSSQL configuration detected:"
    echo "  - Server: $MSSQL_SERVER"
    echo "  - Database: ${MSSQL_DATABASE:-IpRepDB}"
    echo "  - Driver: ${MSSQL_DRIVER:-ODBC Driver 17 for SQL Server}"
else
    echo "No MSSQL configuration detected (MSSQL_SERVER not set)"
fi

# Test ODBC connection if MSSQL is configured
if [ -n "$MSSQL_SERVER" ] && [ -n "$MSSQL_USERNAME" ] && [ -n "$MSSQL_PASSWORD" ]; then
    echo "Testing ODBC connection..."
    python -c "
import pyodbc
import os
try:
    conn_str = f\"DRIVER={{{os.getenv('MSSQL_DRIVER', 'ODBC Driver 17 for SQL Server')}}};SERVER={os.getenv('MSSQL_SERVER')};DATABASE={os.getenv('MSSQL_DATABASE', 'IpRepDB')};UID={os.getenv('MSSQL_USERNAME')};PWD={os.getenv('MSSQL_PASSWORD')};Trusted_Connection=no;Encrypt=yes;TrustServerCertificate=yes;\"
    conn = pyodbc.connect(conn_str, timeout=10)
    cursor = conn.cursor()
    cursor.execute('SELECT 1')
    cursor.fetchone()
    cursor.close()
    conn.close()
    print('✓ ODBC connection test successful')
except Exception as e:
    print(f'✗ ODBC connection test failed: {e}')
    print('Application will continue but database operations may fail')
" || echo "ODBC connection test failed, but continuing..."
fi

echo "Environment setup complete. Starting application..."
echo "----------------------------------------"

# Execute the command passed to the container
exec "$@"
