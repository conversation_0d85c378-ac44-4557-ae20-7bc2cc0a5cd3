"""
MSSQL ODBC handler for zombie IP data processing.
"""

import logging
import pyodbc
import time
from typing import Optional, Dict, Any, Callable
from functools import wraps
from .config import Config

logger = logging.getLogger(__name__)


def retry_on_db_error(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry database operations with exponential backoff.

    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay after each retry

    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (pyodbc.Error, pyodbc.OperationalError, pyodbc.InterfaceError) as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries + 1} attempts: {e}")
                        raise

                    logger.warning(f"Attempt {attempt + 1} of {func.__name__} failed: {e}. "
                                 f"Retrying in {current_delay:.1f} seconds...")
                    time.sleep(current_delay)
                    current_delay *= backoff
                except Exception as e:
                    # Don't retry on non-database errors
                    logger.error(f"Non-retryable error in {func.__name__}: {e}")
                    raise

            # This should never be reached, but just in case
            raise last_exception
        return wrapper
    return decorator


class MSSQLHandler:
    """Handles MSSQL database operations via ODBC."""
    
    def __init__(self, config: Config):
        """Initialize MSSQL handler with configuration."""
        self.config = config
        self.connection = None
        self._validate_config()
    
    def _validate_config(self):
        """Validate MSSQL configuration."""
        try:
            self.config.validate_mssql()
            logger.info("MSSQL configuration validated successfully")
        except ValueError as e:
            logger.error(f"MSSQL configuration validation failed: {e}")
            raise
    
    @retry_on_db_error(max_retries=3, delay=1.0, backoff=2.0)
    def connect(self) -> bool:
        """Establish connection to MSSQL server."""
        try:
            # Build connection string
            connection_string = (
                f"DRIVER={{{self.config.mssql_driver}}};"
                f"SERVER={self.config.mssql_server};"
                f"DATABASE={self.config.mssql_database};"
                f"UID={self.config.mssql_username};"
                f"PWD={self.config.mssql_password};"
                "Trusted_Connection=no;"
                "Encrypt=yes;"
                "TrustServerCertificate=yes;"
            )
            
            logger.info(f"Connecting to MSSQL server: {self.config.mssql_server}")
            logger.info(f"Database: {self.config.mssql_database}")
            
            self.connection = pyodbc.connect(connection_string, timeout=30)
            self.connection.autocommit = True
            
            logger.info("Successfully connected to MSSQL server")
            return True
            
        except pyodbc.Error as e:
            logger.error(f"Failed to connect to MSSQL server: {e}")
            self.connection = None
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MSSQL: {e}")
            self.connection = None
            raise
    
    def disconnect(self):
        """Close connection to MSSQL server."""
        if self.connection:
            try:
                self.connection.close()
                logger.info("Disconnected from MSSQL server")
            except Exception as e:
                logger.warning(f"Error closing MSSQL connection: {e}")
            finally:
                self.connection = None
    
    def is_connected(self) -> bool:
        """Check if connection is active."""
        if not self.connection:
            return False
        
        try:
            # Test connection with a simple query
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception:
            return False
    
    def ensure_connection(self):
        """Ensure connection is active, reconnect if necessary."""
        if not self.is_connected():
            logger.info("Connection lost, attempting to reconnect...")
            try:
                self.connect()
            except Exception as e:
                logger.error(f"Failed to reconnect to database: {e}")
                raise

    @retry_on_db_error(max_retries=5, delay=2.0, backoff=1.5)
    def reconnect_with_retry(self):
        """
        Attempt to reconnect to the database with aggressive retry logic.
        This method is useful for recovering from extended connection failures.

        Returns:
            bool: True if reconnection successful, False otherwise
        """
        try:
            if self.connection:
                try:
                    self.connection.close()
                except Exception:
                    pass  # Ignore errors when closing potentially broken connection
                finally:
                    self.connection = None

            self.connect()
            logger.info("Successfully reconnected to database with retry logic")
            return True

        except Exception as e:
            logger.error(f"Failed to reconnect after retries: {e}")
            return False
    
    @retry_on_db_error(max_retries=3, delay=0.5, backoff=2.0)
    def execute_ip_classification_procedure(self, ip_address: str, country_code: str,
                                          is_dynamic: bool = False) -> bool:
        """
        Execute the p_ZombieIP_IUPD_IPClassification stored procedure.
        
        Args:
            ip_address: The IP address to classify
            country_code: Country code from GeoIP lookup
            is_dynamic: Whether the IP is dynamic (determines class C1 vs C2)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.ensure_connection()
            
            # Determine class based on dynamic status
            ip_class = "C1" if is_dynamic else "C2"
            to_block = True
            script_id = self.config.script_id
            
            # Prepare stored procedure call
            cursor = self.connection.cursor()
            
            # Execute stored procedure
            logger.debug(f"Executing stored procedure for IP: {ip_address}")
            logger.debug(f"Parameters: Class={ip_class}, Country={country_code}, "
                        f"ToBlock={to_block}, ScriptID={script_id}")
            
            cursor.execute(
                "{CALL sp_ZombieIP_IUPD_IPClassification (?, ?, ?, ?, ?, ?)}",
                ip_address, ip_class, 1, country_code, to_block, script_id
            )
            
            # Check if there are any result sets to consume
            while cursor.nextset():
                pass
            
            cursor.close()
            
            logger.info(f"Successfully executed stored procedure for IP: {ip_address}")
            return True
            
        except pyodbc.Error as e:
            logger.error(f"Database error executing stored procedure for IP {ip_address}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error executing stored procedure for IP {ip_address}: {e}")
            return False
    
    @retry_on_db_error(max_retries=2, delay=1.0, backoff=2.0)
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the database connection and return connection info.
        
        Returns:
            dict: Connection test results
        """
        result = {
            'connected': False,
            'server_info': None,
            'database': None,
            'error': None
        }
        
        try:
            self.ensure_connection()
            
            cursor = self.connection.cursor()
            
            # Get server version
            cursor.execute("SELECT @@VERSION")
            server_version = cursor.fetchone()[0]
            
            # Get database name
            cursor.execute("SELECT DB_NAME()")
            database_name = cursor.fetchone()[0]
            
            cursor.close()
            
            result.update({
                'connected': True,
                'server_info': server_version.split('\n')[0],  # First line only
                'database': database_name
            })
            
            logger.info(f"Connection test successful - Database: {database_name}")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Connection test failed: {e}")
        
        return result
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


def determine_ip_class(processed_record: Dict[str, Any]) -> bool:
    """
    Determine if an IP should be classified as dynamic (C1) or static (C2).
    
    This is a placeholder function that currently defaults to static (C2).
    You can enhance this logic based on your specific criteria for determining
    if an IP is dynamic or static.
    
    Args:
        processed_record: The processed IP record with metadata
        
    Returns:
        bool: True if IP is dynamic (C1), False if static (C2)
    """
    # TODO: Implement logic to determine if IP is dynamic
    # This could be based on:
    # - IP ranges known to be dynamic
    # - ISP information
    # - Reverse DNS patterns
    # - Other metadata in the record
    
    # For now, default to static (C2)
    return False
