"""
Configuration module for Zombie IP S3 Ingest.
"""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for the application."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        # AWS Configuration
        self.aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
        self.aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.aws_region = os.getenv('AWS_REGION', 'us-east-1')
        
        # S3 Configuration
        self.s3_bucket_name = os.getenv('S3_BUCKET_NAME')
        self.s3_prefix = os.getenv('S3_PREFIX', '')
        
        # Processing Configuration
        self.file_batch_size = int(os.getenv('FILE_BATCH_SIZE', '10'))  # Files per batch
        self.max_workers = int(os.getenv('MAX_WORKERS', '4'))
        self.enable_parallel_processing = os.getenv('ENABLE_PARALLEL_PROCESSING', 'true').lower() == 'true'

        # Error Handling Configuration
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.retry_delay_seconds = int(os.getenv('RETRY_DELAY_SECONDS', '5'))
        self.continue_on_error = os.getenv('CONTINUE_ON_ERROR', 'true').lower() == 'true'

        # MSSQL Database Configuration
        self.mssql_server = os.getenv('MSSQL_SERVER')
        self.mssql_database = os.getenv('MSSQL_DATABASE', 'IpRepDB')
        self.mssql_username = os.getenv('MSSQL_USERNAME')
        self.mssql_password = os.getenv('MSSQL_PASSWORD')
        self.mssql_driver = os.getenv('MSSQL_DRIVER', 'ODBC Driver 17 for SQL Server')
        self.script_id = int(os.getenv('SCRIPT_ID', '1001'))
        
    def validate(self) -> bool:
        """Validate that required configuration is present."""
        required_fields = [
            'aws_access_key_id',
            'aws_secret_access_key',
            's3_bucket_name'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(self, field):
                missing_fields.append(field.upper())
        
        if missing_fields:
            raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")
        
        return True

    def validate_mssql(self) -> bool:
        """Validate that MSSQL configuration is present."""
        required_mssql_fields = [
            'mssql_server',
            'mssql_username',
            'mssql_password'
        ]

        missing_fields = []
        for field in required_mssql_fields:
            if not getattr(self, field):
                missing_fields.append(field.upper())

        if missing_fields:
            raise ValueError(f"Missing required MSSQL configuration: {', '.join(missing_fields)}")

        return True

    def __repr__(self) -> str:
        """String representation of config (without sensitive data)."""
        return (
            f"Config(aws_region='{self.aws_region}', "
            f"s3_bucket_name='{self.s3_bucket_name}', "
            f"s3_prefix='{self.s3_prefix}', "
            f"file_batch_size={self.file_batch_size}, "
            f"max_workers={self.max_workers})"
        )
