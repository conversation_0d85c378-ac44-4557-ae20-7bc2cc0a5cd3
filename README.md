# Zombie IP S3 Ingest

A Python project for processing and ingesting zombie IP data from S3 with advanced batch processing capabilities.

## Features

- **Batch Processing**: Process multiple S3 files in configurable batches
- **Parallel Processing**: Download and process files concurrently for improved performance
- **Flexible Data Formats**: Supports JSON, JSONL (JSON Lines), and plain text formats
- **Error Handling**: Robust error handling with retry logic and continue-on-error options
- **Progress Tracking**: Detailed logging and statistics for monitoring processing progress
- **Configurable**: Extensive configuration options via environment variables

## Setup

### Option 1: Docker (Recommended)

The easiest way to run this application is using Docker, which includes all dependencies:

```bash
# Copy environment template
cp .env.docker .env
# Edit .env with your configuration

# Build and run
docker-compose up --build
```

See [DOCKER.md](DOCKER.md) for detailed Docker deployment instructions.

### Option 2: Local Installation

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install ODBC Driver (for MSSQL integration):

   **On Ubuntu/Debian:**
   ```bash
   curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
   curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
   apt-get update
   ACCEPT_EULA=Y apt-get install -y msodbcsql17
   ```

   **On macOS:**
   ```bash
   brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
   brew update
   brew install msodbcsql17 mssql-tools
   ```

   **On Windows:**
   Download and install from [Microsoft ODBC Driver 17 for SQL Server](https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server)

4. Configure environment variables (copy from .env.example):
```bash
cp .env.example .env
# Edit .env with your AWS credentials and MSSQL configuration
```

## Usage

### Basic Usage
```bash
python main.py
```

## Configuration

The application uses environment variables for configuration. Key settings include:

- `SCRIPT_ID`: Script ID for stored procedure (default: 1001)

### AWS Configuration
- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `AWS_REGION`: AWS region (default: us-east-1)

### S3 Configuration
- `S3_BUCKET_NAME`: Name of the S3 bucket containing IP data
- `S3_PREFIX`: Prefix/folder path within the bucket (optional)

### Processing Configuration
- `FILE_BATCH_SIZE`: Number of files to process in each batch (default: 10)
- `MAX_WORKERS`: Number of parallel workers for file downloads (default: 4)
- `ENABLE_PARALLEL_PROCESSING`: Enable parallel file processing (default: true)

### Error Handling
- `MAX_RETRIES`: Number of retry attempts for failed operations (default: 3)
- `RETRY_DELAY_SECONDS`: Delay between retry attempts (default: 5)
- `CONTINUE_ON_ERROR`: Continue processing other files if one fails (default: true)

### MSSQL Database Configuration
- `MSSQL_SERVER`: SQL Server hostname (e.g., SQL7)
- `MSSQL_DATABASE`: Database name (default: IpRepDB)
- `MSSQL_USERNAME`: Database username
- `MSSQL_PASSWORD`: Database password
- `MSSQL_DRIVER`: ODBC driver name (default: ODBC Driver 17 for SQL Server)

## Data Format Support

The system automatically detects and processes data formats with **one record per file**:

### JSON Format
Single JSON object with the expected structure:
```json
{"payload": {"identifier": "***********", "detection": {"risk": 95}}}
```

### Plain Text
Single IP address:
```
***********
```

**Note**: Each file should contain exactly one record. If multiple records are found, only the first one will be processed.

## Database Integration

The system includes built-in MSSQL integration that automatically submits processed IP data to the database using the stored procedure `p_ZombieIP_IUPD_IPClassification`.

### Stored Procedure Parameters:
- **IP**: The sender IP address
- **Class**: "C1" if IP is Dynamic, "C2" if static
- **Country**: Location of the IP (GeoIP)
- **ToBlock**: Always set to `true`
- **ScriptID**: Set to `1001` (configurable via `script_id`)

### IP Classification Logic:
The system determines IP classification using the `determine_ip_class()` function:
- **C1 (Dynamic)**: For IPs identified as dynamic
- **C2 (Static)**: For IPs identified as static (default)

You can customize the classification logic in `src/zombie_ip_ingest/mssql_handler.py`.

### Database Connection:
- Uses ODBC connection to SQL Server
- Automatic connection management with retry logic
- Connection testing on startup
- Graceful handling of database unavailability

## Customizing Data Processing

The system now automatically handles database submission. The `_handle_processed_data` method in `src/zombie_ip_ingest/batch_processor.py` includes:

- IP address extraction and validation
- Database submission via stored procedure
- Error handling and retry logic
- Statistics tracking for successful/failed submissions

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Project Structure
```
├── src/                    # Source code
│   └── zombie_ip_ingest/   # Main package
│       ├── app.py          # Main application
│       ├── batch_processor.py  # Batch processing logic
│       ├── config.py       # Configuration management
│       ├── processor.py    # Data processing logic
│       └── s3_client.py    # S3 interaction
├── tests/                  # Test files
├── requirements.txt        # Dependencies
└── main.py                # Entry point
```

## Performance Considerations

- **File Batch Size**: Larger batches reduce S3 API calls but use more memory
- **Parallel Processing**: More workers speed up downloads but increase resource usage
- **Error Handling**: Continue-on-error mode prevents single file failures from stopping the entire process

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request
