# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# S3 Configuration
S3_BUCKET_NAME=your-s3-bucket-name
S3_PREFIX=zombie-ip-data/

# Processing Configuration
FILE_BATCH_SIZE=10
MAX_WORKERS=4
ENABLE_PARALLEL_PROCESSING=true

# Error Handling Configuration
MAX_RETRIES=3
RETRY_DELAY_SECONDS=5
CONTINUE_ON_ERROR=true

# MSSQL Database Configuration
MSSQL_SERVER=SQL7
MSSQL_DATABASE=IpRepDB
MSSQL_USERNAME=your_username
MSSQL_PASSWORD=your_password
MSSQL_DRIVER=ODBC Driver 17 for SQL Server

SCRIPT_ID=1001
