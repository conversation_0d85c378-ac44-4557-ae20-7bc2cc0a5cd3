# Makefile for Zombie IP S3 Ingest Docker operations

.PHONY: help build run stop logs clean test setup

# Default target
help:
	@echo "Available targets:"
	@echo "  setup     - Copy environment template and setup for first run"
	@echo "  build     - Build the Docker image"
	@echo "  run       - Run the application with docker-compose"
	@echo "  run-bg    - Run the application in background"
	@echo "  stop      - Stop the running containers"
	@echo "  logs      - Show container logs"
	@echo "  shell     - Open shell in container"
	@echo "  test      - Run tests in container"
	@echo "  clean     - Remove containers and images"
	@echo "  rebuild   - Clean build and run"

# Setup environment for first run
setup:
	@if [ ! -f .env ]; then \
		cp .env.docker .env; \
		echo "Created .env file from template"; \
		echo "Please edit .env with your configuration before running"; \
	else \
		echo ".env file already exists"; \
	fi

# Build the Docker image
build:
	docker-compose build

# Run the application
run: setup
	docker-compose up

# Run in background
run-bg: setup
	docker-compose up -d

# Stop containers
stop:
	docker-compose down

# Show logs
logs:
	docker-compose logs -f zombie-ip-ingest

# Open shell in container
shell:
	docker-compose run --rm zombie-ip-ingest bash

# Run tests
test:
	docker-compose run --rm zombie-ip-ingest python -m pytest tests/ -v

# Clean up containers and images
clean:
	docker-compose down --rmi all --volumes --remove-orphans
	docker system prune -f

# Rebuild everything
rebuild: clean build run

# Development mode with volume mount
dev:
	docker run --env-file .env \
		-v $(PWD):/app \
		-it zombie-ip-s3-ingest bash

# Check ODBC drivers in container
check-odbc:
	docker-compose run --rm zombie-ip-ingest odbcinst -q -d

# Test database connection
test-db:
	docker-compose run --rm zombie-ip-ingest python -c "\
import os; \
import pyodbc; \
print('Testing ODBC connection...'); \
conn_str = f'DRIVER={{{os.getenv(\"MSSQL_DRIVER\", \"ODBC Driver 17 for SQL Server\")}}};SERVER={os.getenv(\"MSSQL_SERVER\")};DATABASE={os.getenv(\"MSSQL_DATABASE\", \"IpRepDB\")};UID={os.getenv(\"MSSQL_USERNAME\")};PWD={os.getenv(\"MSSQL_PASSWORD\")};Trusted_Connection=no;Encrypt=yes;TrustServerCertificate=yes;'; \
conn = pyodbc.connect(conn_str, timeout=10); \
print('✓ Connection successful'); \
conn.close()"
